<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Response Code Analysis Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header h2 {
            font-size: 1.3em;
            opacity: 0.9;
            font-weight: 300;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.8;
            font-size: 1em;
            font-style: italic;
        }
        
        .summary-section {
            padding: 30px 40px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .summary-section h3 {
            color: #ff6b6b;
            margin-bottom: 15px;
            font-size: 1.4em;
            display: flex;
            align-items: center;
        }
        
        .summary-section h3::before {
            content: '📊';
            margin-right: 10px;
        }
        
        .summary-section p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .metrics-section {
            padding: 20px 40px;
            background: white;
        }
        
        .metrics-title {
            color: #ff6b6b;
            font-size: 1.3em;
            margin-bottom: 20px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .metrics-title::before {
            content: '🔑';
            margin-right: 10px;
        }
        
        .metrics-list {
            list-style: none;
            padding: 0;
        }
        
        .metrics-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .metrics-list li:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: bold;
            color: #333;
            margin-right: 10px;
        }
        
        .metric-value {
            color: #666;
        }
        
        .success-rate {
            color: #28a745;
            font-weight: bold;
        }
        
        .error-rate {
            color: #dc3545;
            font-weight: bold;
        }
        
        .content-section {
            padding: 30px 40px;
        }
        
        .section-title {
            color: #ff6b6b;
            font-size: 1.4em;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ff6b6b;
            display: flex;
            align-items: center;
            font-weight: bold;
        }
        
        .section-title::before {
            content: '📈';
            margin-right: 10px;
        }
        
        .chart-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        
        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .data-table th {
            background: #ff6b6b;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 1px;
        }
        
        .data-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .status-200 {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-400 {
            color: #ffc107;
            font-weight: bold;
        }
        
        .percentage {
            font-weight: bold;
            color: #ff6b6b;
        }
        
        .endpoint-path {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        
        .method-get {
            color: #28a745;
            font-weight: bold;
        }
        
        .method-post {
            color: #007bff;
            font-weight: bold;
        }
        
        .success-rate-cell {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 API Response Code Analysis Report</h1>
            <h2>HTTP Status Code Distribution</h2>
            <p><em>Report Generated: June 29, 2025</em></p>
        </div>
        
        <div class="summary-section">
            <h3>Summary</h3>
            <p>This report analyzes the distribution and patterns of HTTP status codes in your API requests, helping to identify successful operations and potential issues.</p>
        </div>
        
        <div class="metrics-section">
            <div class="metrics-title">Key Metrics</div>
            <ul class="metrics-list">
                <li>
                    <span class="metric-label">Total Requests Analyzed:</span>
                    <span class="metric-value">5</span>
                </li>
                <li>
                    <span class="metric-label">Success Rate (2xx):</span>
                    <span class="metric-value success-rate">80% (4 requests)</span>
                </li>
                <li>
                    <span class="metric-label">Error Rate (4xx/5xx):</span>
                    <span class="metric-value error-rate">20% (1 request)</span>
                </li>
                <li>
                    <span class="metric-label">Time Period:</span>
                    <span class="metric-value">2025-06-29 10:00:00 to 2025-06-29 10:30:00</span>
                </li>
            </ul>
        </div>
        
        <div class="content-section">
            <div class="section-title">Status Code Distribution</div>
            <div class="chart-container">
                <div class="chart-wrapper">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Status Code</th>
                        <th>Count</th>
                        <th>Percentage</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="status-200">200</td>
                        <td>4</td>
                        <td class="percentage">80%</td>
                        <td>OK</td>
                    </tr>
                    <tr>
                        <td class="status-400">400</td>
                        <td>1</td>
                        <td class="percentage">20%</td>
                        <td>Bad Request</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="section-title">Success Rate by Endpoint</div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Endpoint</th>
                        <th>HTTP Method</th>
                        <th>Total Requests</th>
                        <th>Success Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="endpoint-path">/api/login</span></td>
                        <td><span class="method-post">POST</span></td>
                        <td>2</td>
                        <td class="success-rate-cell">50%</td>
                    </tr>
                    <tr>
                        <td><span class="endpoint-path">/api/items</span></td>
                        <td><span class="method-get">GET</span></td>
                        <td>1</td>
                        <td class="success-rate-cell">100%</td>
                    </tr>
                    <tr>
                        <td><span class="endpoint-path">/api/items/123</span></td>
                        <td><span class="method-get">GET</span></td>
                        <td>1</td>
                        <td class="success-rate-cell">100%</td>
                    </tr>
                    <tr>
                        <td><span class="endpoint-path">/api/items</span></td>
                        <td><span class="method-post">POST</span></td>
                        <td>1</td>
                        <td class="success-rate-cell">100%</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Status Code Distribution Chart
        const ctx = document.getElementById('statusChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['200 (OK)', '400 (Bad Request)'],
                datasets: [{
                    data: [4, 1],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(255, 193, 7, 0.8)'
                    ],
                    borderColor: [
                        '#28a745',
                        '#ffc107'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 14
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: 'HTTP Status Code Distribution',
                        font: {
                            size: 18,
                            weight: 'bold'
                        },
                        color: '#333'
                    }
                }
            }
        });
    </script>
</body>
</html>
