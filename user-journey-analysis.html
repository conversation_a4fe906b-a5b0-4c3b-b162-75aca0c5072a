<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Journey - Request Chain Analysis</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .journey-stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .timeline-container {
            padding: 40px;
            position: relative;
        }
        
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #4facfe, #00f2fe);
            border-radius: 2px;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            padding-left: 40px;
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }
        
        .timeline-item:nth-child(1) { animation-delay: 0.1s; }
        .timeline-item:nth-child(2) { animation-delay: 0.2s; }
        .timeline-item:nth-child(3) { animation-delay: 0.3s; }
        .timeline-item:nth-child(4) { animation-delay: 0.4s; }
        .timeline-item:nth-child(5) { animation-delay: 0.5s; }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -47px;
            top: 8px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 2;
        }
        
        .timeline-item.success::before {
            background-color: #28a745;
        }
        
        .timeline-item.info::before {
            background-color: #17a2b8;
        }
        
        .timeline-item.warning::before {
            background-color: #ffc107;
        }
        
        .timeline-item.danger::before {
            background-color: #dc3545;
        }
        
        .timeline-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #4facfe;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .timeline-content:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .timeline-content.success {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .timeline-content.info {
            border-left-color: #17a2b8;
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        }
        
        .timeline-content.warning {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        }
        
        .timeline-content.danger {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }
        
        .timeline-time {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .timeline-time::before {
            content: '🕐';
            margin-right: 8px;
        }
        
        .timeline-action {
            font-size: 1.1em;
            color: #555;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .timeline-details {
            font-size: 0.9em;
            color: #666;
            line-height: 1.5;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
            margin-top: 8px;
        }
        
        .status-success {
            background-color: #28a745;
            color: white;
        }
        
        .status-info {
            background-color: #17a2b8;
            color: white;
        }
        
        .status-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .status-danger {
            background-color: #dc3545;
            color: white;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .journey-summary {
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .journey-summary h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .summary-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .summary-card h4 {
            margin: 0 0 10px 0;
            color: #4facfe;
        }
        
        .summary-card p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 User Journey Analysis</h1>
            <p>Request Chain Analysis & User Behavior Tracking</p>
        </div>
        
        <div class="journey-stats">
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">Total Actions</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">25:15</div>
                <div class="stat-label">Session Duration</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">80%</div>
                <div class="stat-label">Success Rate</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">1</div>
                <div class="stat-label">Failed Attempts</div>
            </div>
        </div>
        
        <div class="timeline-container">
            <div class="timeline">
                <div class="timeline-item success">
                    <div class="timeline-content success">
                        <div class="timeline-time">10:00:15</div>
                        <div class="timeline-action">🔐 Successful Login</div>
                        <div class="timeline-details">
                            User successfully authenticated and gained access to the system. 
                            Session initiated with proper credentials.
                        </div>
                        <span class="status-badge status-success">Success</span>
                    </div>
                </div>
                
                <div class="timeline-item info">
                    <div class="timeline-content info">
                        <div class="timeline-time">10:10:30</div>
                        <div class="timeline-action">📋 Browsed Items List</div>
                        <div class="timeline-details">
                            User navigated to the items catalog and browsed available products/services. 
                            Spent time exploring the available options.
                        </div>
                        <span class="status-badge status-info">Navigation</span>
                    </div>
                </div>
                
                <div class="timeline-item danger">
                    <div class="timeline-content danger">
                        <div class="timeline-time">10:15:00</div>
                        <div class="timeline-action">❌ Failed Login Attempt</div>
                        <div class="timeline-details">
                            Authentication failed - possibly due to session timeout or incorrect credentials. 
                            User may have tried to access a restricted area.
                        </div>
                        <span class="status-badge status-danger">Failed</span>
                    </div>
                </div>
                
                <div class="timeline-item success">
                    <div class="timeline-content success">
                        <div class="timeline-time">10:20:45</div>
                        <div class="timeline-action">➕ Created New Item</div>
                        <div class="timeline-details">
                            User successfully created a new item/record in the system. 
                            This indicates active engagement and content creation.
                        </div>
                        <span class="status-badge status-success">Created</span>
                    </div>
                </div>
                
                <div class="timeline-item info">
                    <div class="timeline-content info">
                        <div class="timeline-time">10:25:30</div>
                        <div class="timeline-action">👁️ Viewed Item Details</div>
                        <div class="timeline-details">
                            User accessed detailed information about a specific item. 
                            This shows continued engagement and interest in the content.
                        </div>
                        <span class="status-badge status-info">View</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="journey-summary">
            <h3>📊 Journey Summary & Insights</h3>
            <p>This user session shows a typical workflow with one authentication hiccup. The user remained engaged throughout the session and successfully completed productive actions.</p>
            
            <div class="summary-grid">
                <div class="summary-card">
                    <h4>Session Start</h4>
                    <p>10:00:15<br>Successful Login</p>
                </div>
                <div class="summary-card">
                    <h4>Session End</h4>
                    <p>10:25:30<br>Item Details View</p>
                </div>
                <div class="summary-card">
                    <h4>Key Actions</h4>
                    <p>Browse → Create → View<br>Productive Session</p>
                </div>
                <div class="summary-card">
                    <h4>Issues</h4>
                    <p>1 Failed Login<br>Possible Session Timeout</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
