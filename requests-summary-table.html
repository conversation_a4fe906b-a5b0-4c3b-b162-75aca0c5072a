<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Requests Summary by IP Address</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: white;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .ip-address {
            font-weight: bold;
            color: #007bff;
        }
        
        .status-codes {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            margin: 2px 0;
        }
        
        .status-200 { color: #28a745; }
        .status-400 { color: #ffc107; }
        .status-500 { color: #dc3545; }
        
        .total-requests {
            font-weight: bold;
            color: #333;
        }
        
        .time-info {
            color: #666;
            font-size: 0.9em;
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Requests Summary by IP Address / Email Address / Role</h1>

        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-number">18</div>
                <div class="stat-label">Total Records</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">141</div>
                <div class="stat-label">Total Requests</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">3</div>
                <div class="stat-label">Data Types</div>
            </div>
        </div>
        
        <!-- IP Address Table -->
        <h2 style="color: #007bff; margin-top: 40px; margin-bottom: 20px; border-left: 4px solid #007bff; padding-left: 15px;">
            📍 Requests Summary by IP Address
        </h2>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>IP Address</th>
                        <th>First Seen</th>
                        <th>Last Seen</th>
                        <th>Total Requests</th>
                        <th>Status Code Summary</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="ip-address">***********</td>
                        <td class="time-info">10:00:15</td>
                        <td class="time-info">10:25:30</td>
                        <td class="total-requests">5</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 2 requests</div>
                                <div class="status-400">400: 1 request</div>
                                <div class="status-500">500: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address">*********</td>
                        <td class="time-info">09:45:22</td>
                        <td class="time-info">11:30:45</td>
                        <td class="total-requests">12</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 10 requests</div>
                                <div class="status-400">400: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address">************</td>
                        <td class="time-info">08:15:10</td>
                        <td class="time-info">08:45:33</td>
                        <td class="total-requests">8</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 6 requests</div>
                                <div class="status-500">500: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address">************</td>
                        <td class="time-info">14:20:05</td>
                        <td class="time-info">15:10:18</td>
                        <td class="total-requests">15</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 12 requests</div>
                                <div class="status-400">400: 1 request</div>
                                <div class="status-500">500: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address">*************</td>
                        <td class="time-info">16:30:12</td>
                        <td class="time-info">16:35:28</td>
                        <td class="total-requests">3</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-400">400: 2 requests</div>
                                <div class="status-500">500: 1 request</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address">*************</td>
                        <td class="time-info">12:05:30</td>
                        <td class="time-info">13:45:15</td>
                        <td class="total-requests">4</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 3 requests</div>
                                <div class="status-400">400: 1 request</div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Email Address Table -->
        <h2 style="color: #28a745; margin-top: 40px; margin-bottom: 20px; border-left: 4px solid #28a745; padding-left: 15px;">
            📧 Requests Summary by Email Address
        </h2>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Email Address</th>
                        <th>First Seen</th>
                        <th>Last Seen</th>
                        <th>Total Requests</th>
                        <th>Status Code Summary</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="ip-address" style="color: #28a745;"><EMAIL></td>
                        <td class="time-info">10:00:15</td>
                        <td class="time-info">10:25:30</td>
                        <td class="total-requests">5</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 2 requests</div>
                                <div class="status-400">400: 1 request</div>
                                <div class="status-500">500: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #28a745;"><EMAIL></td>
                        <td class="time-info">09:30:45</td>
                        <td class="time-info">11:15:20</td>
                        <td class="total-requests">18</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 15 requests</div>
                                <div class="status-400">400: 2 requests</div>
                                <div class="status-500">500: 1 request</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #28a745;"><EMAIL></td>
                        <td class="time-info">14:20:10</td>
                        <td class="time-info">16:45:35</td>
                        <td class="total-requests">12</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 10 requests</div>
                                <div class="status-400">400: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #28a745;"><EMAIL></td>
                        <td class="time-info">13:15:25</td>
                        <td class="time-info">13:45:50</td>
                        <td class="total-requests">7</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 4 requests</div>
                                <div class="status-400">400: 2 requests</div>
                                <div class="status-500">500: 1 request</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #28a745;"><EMAIL></td>
                        <td class="time-info">08:45:30</td>
                        <td class="time-info">17:30:15</td>
                        <td class="total-requests">25</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 20 requests</div>
                                <div class="status-400">400: 3 requests</div>
                                <div class="status-500">500: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #28a745;"><EMAIL></td>
                        <td class="time-info">15:10:40</td>
                        <td class="time-info">15:25:55</td>
                        <td class="total-requests">3</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-400">400: 2 requests</div>
                                <div class="status-500">500: 1 request</div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Role Table -->
        <h2 style="color: #dc3545; margin-top: 40px; margin-bottom: 20px; border-left: 4px solid #dc3545; padding-left: 15px;">
            👤 Requests Summary by Role
        </h2>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Role</th>
                        <th>First Seen</th>
                        <th>Last Seen</th>
                        <th>Total Requests</th>
                        <th>Status Code Summary</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="ip-address" style="color: #dc3545;">admin</td>
                        <td class="time-info">10:00:15</td>
                        <td class="time-info">10:25:30</td>
                        <td class="total-requests">5</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 2 requests</div>
                                <div class="status-400">400: 1 request</div>
                                <div class="status-500">500: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #dc3545;">user</td>
                        <td class="time-info">09:15:20</td>
                        <td class="time-info">16:30:45</td>
                        <td class="total-requests">32</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 28 requests</div>
                                <div class="status-400">400: 3 requests</div>
                                <div class="status-500">500: 1 request</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #dc3545;">moderator</td>
                        <td class="time-info">11:45:10</td>
                        <td class="time-info">15:20:25</td>
                        <td class="total-requests">14</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 12 requests</div>
                                <div class="status-400">400: 1 request</div>
                                <div class="status-500">500: 1 request</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #dc3545;">guest</td>
                        <td class="time-info">13:30:15</td>
                        <td class="time-info">14:15:40</td>
                        <td class="total-requests">8</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 5 requests</div>
                                <div class="status-400">400: 2 requests</div>
                                <div class="status-500">500: 1 request</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #dc3545;">developer</td>
                        <td class="time-info">08:00:05</td>
                        <td class="time-info">17:45:30</td>
                        <td class="total-requests">22</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 18 requests</div>
                                <div class="status-400">400: 2 requests</div>
                                <div class="status-500">500: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="ip-address" style="color: #dc3545;">api_client</td>
                        <td class="time-info">12:20:30</td>
                        <td class="time-info">12:35:45</td>
                        <td class="total-requests">6</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 4 requests</div>
                                <div class="status-400">400: 1 request</div>
                                <div class="status-500">500: 1 request</div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <h3>Legend:</h3>
            <ul>
                <li><strong>📍 IP Address:</strong> Network identifier (Primary key)</li>
                <li><strong>📧 Email Address:</strong> User email identifier (Primary key)</li>
                <li><strong>👤 Role:</strong> User role/permission level (Primary key)</li>
                <li><strong>First/Last Seen:</strong> Time range of activity</li>
                <li><strong>Total Requests:</strong> Number of requests made</li>
                <li><strong>Status Codes:</strong>
                    <span class="status-200">200 (Success)</span>,
                    <span class="status-400">400 (Client Error)</span>,
                    <span class="status-500">500 (Server Error)</span>
                </li>
            </ul>
        </div>
    </div>
</body>
</html>
