<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Requests Summary by IP Address</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: white;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background-color: #f8f9fa;
        }
        
        .ip-address {
            font-weight: bold;
            color: #007bff;
        }
        
        .status-codes {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            margin: 2px 0;
        }
        
        .status-200 { color: #28a745; }
        .status-400 { color: #ffc107; }
        .status-500 { color: #dc3545; }
        
        .total-requests {
            font-weight: bold;
            color: #333;
        }
        
        .time-info {
            color: #666;
            font-size: 0.9em;
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Requests Summary by IP Address / Email Address / Role</h1>
        
        <div class="summary-stats">
            <div class="stat-item">
                <div class="stat-number">1</div>
                <div class="stat-label">Total IPs</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">Total Requests</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">3</div>
                <div class="stat-label">Status Types</div>
            </div>
        </div>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>IP Address</th>
                        <th>First Seen</th>
                        <th>Last Seen</th>
                        <th>Total Requests</th>
                        <th>Status Code Summary</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="ip-address">***********</td>
                        <td class="time-info">10:00:15</td>
                        <td class="time-info">10:25:30</td>
                        <td class="total-requests">5</td>
                        <td>
                            <div class="status-codes">
                                <div class="status-200">200: 2 requests</div>
                                <div class="status-400">400: 1 request</div>
                                <div class="status-500">500: 2 requests</div>
                            </div>
                        </td>
                    </tr>
                    <!-- Additional rows can be added here -->
                </tbody>
            </table>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <h3>Legend:</h3>
            <ul>
                <li><strong>IP Address:</strong> Primary key identifier</li>
                <li><strong>First/Last Seen:</strong> Time range of activity</li>
                <li><strong>Total Requests:</strong> Number of requests from this IP</li>
                <li><strong>Status Codes:</strong> 
                    <span class="status-200">200 (Success)</span>, 
                    <span class="status-400">400 (Client Error)</span>, 
                    <span class="status-500">500 (Server Error)</span>
                </li>
            </ul>
        </div>
    </div>
</body>
</html>
