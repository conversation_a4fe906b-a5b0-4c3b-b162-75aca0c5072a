<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Request Volume Analysis Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.8em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header h2 {
            font-size: 1.4em;
            opacity: 0.9;
            font-weight: 300;
            margin-bottom: 5px;
        }
        
        .header p {
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .summary-section {
            padding: 30px 40px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        
        .summary-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .summary-section p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .content-section {
            padding: 40px;
        }
        
        .section-title {
            color: #333;
            font-size: 1.8em;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: '📊';
            margin-right: 10px;
        }
        
        .chart-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        
        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .data-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 1px;
        }
        
        .data-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .percentage {
            font-weight: bold;
            color: #667eea;
        }
        
        .method-get {
            color: #28a745;
            font-weight: bold;
        }
        
        .method-post {
            color: #007bff;
            font-weight: bold;
        }
        
        .endpoint-path {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        
        .filter-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        
        .filter-note strong {
            color: #533f03;
        }
        
        .insights-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            margin-top: 30px;
            border-radius: 10px;
        }
        
        .insights-section h3 {
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .insights-list {
            list-style: none;
            padding: 0;
        }
        
        .insights-list li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
        }
        
        .insights-list li::before {
            content: '💡';
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 API Request Volume Analysis Report</h1>
            <h2>Traffic Patterns and Usage Statistics</h2>
            <p><em>Report Generated: June 29, 2025</em></p>
        </div>
        
        <div class="summary-section">
            <h3>📋 Summary</h3>
            <p>This report provides insights into API request volumes, helping to understand usage patterns, peak times, and endpoint popularity.</p>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-number">5</div>
                    <div class="metric-label">Total Requests</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">30</div>
                    <div class="metric-label">Minutes Period</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">0.17</div>
                    <div class="metric-label">Requests/Min</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">4</div>
                    <div class="metric-label">Unique Endpoints</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number">1</div>
                    <div class="metric-label">Unique Users</div>
                </div>
            </div>
        </div>
        
        <div class="content-section">
            <div class="section-title">Request Volume by Time</div>
            <div class="chart-container">
                <div class="chart-wrapper">
                    <canvas id="volumeChart"></canvas>
                </div>
                <div class="filter-note">
                    <strong>📌 Note:</strong> Need a very good area graph for this data.<br>
                    <strong>🔍 Filter required:</strong> Time period, interval and endpoint filter
                </div>
            </div>
            
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Time Window</th>
                        <th>Request Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>10:00 - 10:05</td>
                        <td>1</td>
                        <td class="percentage">20%</td>
                    </tr>
                    <tr>
                        <td>10:05 - 10:10</td>
                        <td>0</td>
                        <td class="percentage">0%</td>
                    </tr>
                    <tr>
                        <td>10:10 - 10:15</td>
                        <td>1</td>
                        <td class="percentage">20%</td>
                    </tr>
                    <tr>
                        <td>10:15 - 10:20</td>
                        <td>1</td>
                        <td class="percentage">20%</td>
                    </tr>
                    <tr>
                        <td>10:25 - 10:30</td>
                        <td>2</td>
                        <td class="percentage">40%</td>
                    </tr>
                </tbody>
            </table>

            <div class="section-title">Endpoint Popularity</div>
            <div class="chart-container">
                <div class="chart-wrapper">
                    <canvas id="endpointChart"></canvas>
                </div>
            </div>

            <table class="data-table">
                <thead>
                    <tr>
                        <th>Endpoint</th>
                        <th>HTTP Method</th>
                        <th>Request Count</th>
                        <th>% of Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="endpoint-path">/api/login</span></td>
                        <td><span class="method-post">POST</span></td>
                        <td>2</td>
                        <td class="percentage">40%</td>
                    </tr>
                    <tr>
                        <td><span class="endpoint-path">/api/items</span></td>
                        <td><span class="method-get">GET</span></td>
                        <td>1</td>
                        <td class="percentage">20%</td>
                    </tr>
                    <tr>
                        <td><span class="endpoint-path">/api/items/123</span></td>
                        <td><span class="method-get">GET</span></td>
                        <td>1</td>
                        <td class="percentage">20%</td>
                    </tr>
                    <tr>
                        <td><span class="endpoint-path">/api/items</span></td>
                        <td><span class="method-post">POST</span></td>
                        <td>1</td>
                        <td class="percentage">20%</td>
                    </tr>
                </tbody>
            </table>

            <div class="section-title">HTTP Method Distribution</div>
            <div class="chart-container">
                <div class="chart-wrapper">
                    <canvas id="methodChart"></canvas>
                </div>
            </div>

            <table class="data-table">
                <thead>
                    <tr>
                        <th>HTTP Method</th>
                        <th>Request Count</th>
                        <th>% of Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="method-get">GET</span></td>
                        <td>2</td>
                        <td class="percentage">40%</td>
                    </tr>
                    <tr>
                        <td><span class="method-post">POST</span></td>
                        <td>3</td>
                        <td class="percentage">60%</td>
                    </tr>
                </tbody>
            </table>

            <div class="section-title">User Activity</div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>User Email</th>
                        <th>Request Count</th>
                        <th>First Active</th>
                        <th>Last Active</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><EMAIL></td>
                        <td>5</td>
                        <td>Jun 29, 2025 10:00:15 AM</td>
                        <td>Jun 29, 2025 10:25:30 AM</td>
                    </tr>
                </tbody>
            </table>

            <div class="insights-section">
                <h3>🔍 Key Insights & Recommendations</h3>
                <ul class="insights-list">
                    <li><strong>Peak Activity:</strong> Highest request volume occurred in the 10:25-10:30 window with 2 requests (40% of total)</li>
                    <li><strong>Endpoint Usage:</strong> /api/login is the most popular endpoint, accounting for 40% of all requests</li>
                    <li><strong>Method Distribution:</strong> POST requests dominate with 60% of traffic, indicating active data submission</li>
                    <li><strong>User Engagement:</strong> Single user session lasted 25 minutes with consistent activity</li>
                    <li><strong>Recommendation:</strong> Consider implementing caching for frequently accessed endpoints like /api/items</li>
                    <li><strong>Monitoring:</strong> Set up alerts for unusual spikes in login attempts to detect potential security issues</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Request Volume Chart
        const ctx = document.getElementById('volumeChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['10:00-10:05', '10:05-10:10', '10:10-10:15', '10:15-10:20', '10:20-10:25', '10:25-10:30'],
                datasets: [{
                    label: 'Request Count',
                    data: [1, 0, 1, 1, 0, 2],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'API Request Volume Over Time',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#333'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                }
            }
        });

        // Endpoint Popularity Chart
        const endpointCtx = document.getElementById('endpointChart').getContext('2d');
        new Chart(endpointCtx, {
            type: 'bar',
            data: {
                labels: ['/api/login (POST)', '/api/items (GET)', '/api/items/123 (GET)', '/api/items (POST)'],
                datasets: [{
                    label: 'Request Count',
                    data: [2, 1, 1, 1],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(102, 126, 234, 0.8)'
                    ],
                    borderColor: [
                        '#667eea',
                        '#28a745',
                        '#28a745',
                        '#667eea'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Most Popular API Endpoints',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#333'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // HTTP Method Distribution Chart
        const methodCtx = document.getElementById('methodChart').getContext('2d');
        new Chart(methodCtx, {
            type: 'doughnut',
            data: {
                labels: ['GET', 'POST'],
                datasets: [{
                    data: [2, 3],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(0, 123, 255, 0.8)'
                    ],
                    borderColor: [
                        '#28a745',
                        '#007bff'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 14
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: 'HTTP Method Distribution',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        color: '#333'
                    }
                }
            }
        });
    </script>
</body>
</html>
