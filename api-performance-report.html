<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Endpoint Performance Report</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2"># API Endpoint Performance Report</h1>
            <h2 class="text-xl text-gray-600 mb-1">## Average Response Time Analysis</h2>
            <p class="text-sm text-gray-500">*Report Generated: June 29, 2025*</p>
        </div>

        <!-- Summary Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">### Summary</h3>
            <p class="text-gray-700 leading-relaxed">
                This report provides an analysis of average response times across different API endpoints, 
                helping identify performance bottlenecks and optimize API responsiveness.
            </p>
        </div>

        <!-- Key Metrics -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">### Key Metrics</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                    <div class="flex items-center">
                        <span class="text-blue-600 font-medium">**Total Requests Analyzed**:</span>
                        <span class="ml-2 text-2xl font-bold text-blue-800" id="totalRequests">5</span>
                    </div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                    <div class="flex items-center">
                        <span class="text-green-600 font-medium">**Unique Endpoints**:</span>
                        <span class="ml-2 text-2xl font-bold text-green-800" id="uniqueEndpoints">4</span>
                    </div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-500">
                    <div class="flex items-center">
                        <span class="text-purple-600 font-medium">**Time Period**:</span>
                        <span class="ml-2 text-sm font-semibold text-purple-800" id="timePeriod">2025-06-29 10:00:00 to 2025-06-29 10:30:00</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Table -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">### Average Response Time by Endpoint</h3>
            <div class="overflow-x-auto">
                <table class="w-full table-auto border-collapse">
                    <thead>
                        <tr class="bg-gray-100">
                            <th class="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-700">Endpoint</th>
                            <th class="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-700">HTTP Method</th>
                            <th class="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-700">Request Count</th>
                            <th class="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-700">Avg Response Time (ms)</th>
                            <th class="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-700">Min Response Time (ms)</th>
                            <th class="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-700">Max Response Time (ms)</th>
                            <th class="border border-gray-300 px-4 py-3 text-left font-semibold text-gray-700">Status</th>
                        </tr>
                    </thead>
                    <tbody id="performanceTableBody">
                        <!-- Data will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Response Time Comparison</h3>
                <canvas id="responseTimeChart" width="400" height="300"></canvas>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Request Distribution</h3>
                <canvas id="requestDistributionChart" width="400" height="300"></canvas>
            </div>
        </div>

        <!-- Performance Insights -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Performance Insights</h3>
            <div id="performanceInsights" class="space-y-3">
                <!-- Insights will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // API Performance Data
        const performanceData = [
            {
                endpoint: '/api/login',
                method: 'POST',
                requestCount: 2,
                avgResponseTime: 1100.0,
                minResponseTime: 1000.0,
                maxResponseTime: 1200.0
            },
            {
                endpoint: '/api/items',
                method: 'GET',
                requestCount: 1,
                avgResponseTime: 150.0,
                minResponseTime: 150.0,
                maxResponseTime: 150.0
            },
            {
                endpoint: '/api/items/123',
                method: 'GET',
                requestCount: 1,
                avgResponseTime: 200.0,
                minResponseTime: 200.0,
                maxResponseTime: 200.0
            },
            {
                endpoint: '/api/items',
                method: 'POST',
                requestCount: 1,
                avgResponseTime: 180.0,
                minResponseTime: 180.0,
                maxResponseTime: 180.0
            }
        ];

        // Function to get performance status based on response time
        function getPerformanceStatus(avgTime) {
            if (avgTime < 200) return { status: 'Excellent', class: 'bg-green-100 text-green-800' };
            if (avgTime < 500) return { status: 'Good', class: 'bg-yellow-100 text-yellow-800' };
            if (avgTime < 1000) return { status: 'Fair', class: 'bg-orange-100 text-orange-800' };
            return { status: 'Poor', class: 'bg-red-100 text-red-800' };
        }

        // Populate performance table
        function populateTable() {
            const tbody = document.getElementById('performanceTableBody');
            tbody.innerHTML = '';

            performanceData.forEach(data => {
                const status = getPerformanceStatus(data.avgResponseTime);
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                row.innerHTML = `
                    <td class="border border-gray-300 px-4 py-3 font-mono text-sm">${data.endpoint}</td>
                    <td class="border border-gray-300 px-4 py-3">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full ${data.method === 'GET' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">${data.method}</span>
                    </td>
                    <td class="border border-gray-300 px-4 py-3 text-center">${data.requestCount}</td>
                    <td class="border border-gray-300 px-4 py-3 text-center font-semibold">${data.avgResponseTime}</td>
                    <td class="border border-gray-300 px-4 py-3 text-center">${data.minResponseTime}</td>
                    <td class="border border-gray-300 px-4 py-3 text-center">${data.maxResponseTime}</td>
                    <td class="border border-gray-300 px-4 py-3 text-center">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full ${status.class}">${status.status}</span>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Create response time chart
        function createResponseTimeChart() {
            const ctx = document.getElementById('responseTimeChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: performanceData.map(d => `${d.method} ${d.endpoint}`),
                    datasets: [{
                        label: 'Average Response Time (ms)',
                        data: performanceData.map(d => d.avgResponseTime),
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(168, 85, 247, 0.8)'
                        ],
                        borderColor: [
                            'rgba(239, 68, 68, 1)',
                            'rgba(34, 197, 94, 1)',
                            'rgba(59, 130, 246, 1)',
                            'rgba(168, 85, 247, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Response Time (ms)'
                            }
                        }
                    }
                }
            });
        }

        // Create request distribution chart
        function createRequestDistributionChart() {
            const ctx = document.getElementById('requestDistributionChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: performanceData.map(d => `${d.method} ${d.endpoint}`),
                    datasets: [{
                        data: performanceData.map(d => d.requestCount),
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(34, 197, 94, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(168, 85, 247, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Generate performance insights
        function generateInsights() {
            const insightsContainer = document.getElementById('performanceInsights');
            const slowestEndpoint = performanceData.reduce((prev, current) => 
                prev.avgResponseTime > current.avgResponseTime ? prev : current
            );
            const fastestEndpoint = performanceData.reduce((prev, current) => 
                prev.avgResponseTime < current.avgResponseTime ? prev : current
            );

            const insights = [
                {
                    type: 'warning',
                    title: 'Slowest Endpoint',
                    message: `${slowestEndpoint.method} ${slowestEndpoint.endpoint} has the highest average response time at ${slowestEndpoint.avgResponseTime}ms`,
                    class: 'bg-red-50 border-red-200 text-red-800'
                },
                {
                    type: 'success',
                    title: 'Fastest Endpoint',
                    message: `${fastestEndpoint.method} ${fastestEndpoint.endpoint} performs best with ${fastestEndpoint.avgResponseTime}ms average response time`,
                    class: 'bg-green-50 border-green-200 text-green-800'
                },
                {
                    type: 'info',
                    title: 'Recommendation',
                    message: 'Consider optimizing the /api/login endpoint as it shows significantly higher response times compared to other endpoints',
                    class: 'bg-blue-50 border-blue-200 text-blue-800'
                }
            ];

            insights.forEach(insight => {
                const div = document.createElement('div');
                div.className = `p-4 rounded-lg border ${insight.class}`;
                div.innerHTML = `
                    <h4 class="font-semibold mb-2">${insight.title}</h4>
                    <p class="text-sm">${insight.message}</p>
                `;
                insightsContainer.appendChild(div);
            });
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            populateTable();
            createResponseTimeChart();
            createRequestDistributionChart();
            generateInsights();
        });
    </script>
</body>
</html>
